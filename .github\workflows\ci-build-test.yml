name: Build and Test

on:
  # Manual trigger
  workflow_dispatch:

  # Run CI for all pushes to main
  push:
    branches: ["main"]

  # Run CI for pull requests to all branches, but only if code changed
  pull_request:
    paths:
      - ".github/workflows/ci-*.yml"
      - "*.sln"
      - "*.props"
      - "Makefile"
      - "src/**"
      - "tests/**"
      - "samples/**"

permissions:
  contents: read

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        configuration: [Debug, Release]
      fail-fast: false

    runs-on: ${{ matrix.os }}

    steps:
    - name: Clone the repo
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis

    - name: Set up .NET
      uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9 # v4.3.1
      with:
        dotnet-version: |
          9.0.x
          8.0.x

    # NetFX testing on non-Windows requires mono
    - name: Setup Mono
      if: runner.os == 'Linux'
      run: sudo apt-get install -y mono-devel

    - name: Set up Node.js
      uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e # v4.3.0
      with:
        node-version: '20'

    - name: Install dependencies for tests
      run: npm install @modelcontextprotocol/server-everything

    - name: Install dependencies for tests
      run: npm install @modelcontextprotocol/server-memory

    - name: Build
      run: dotnet build --configuration ${{ matrix.configuration }}

    - name: Pack
      run: dotnet pack --configuration ${{ matrix.configuration }}

    - name: Test
      run: >-
        dotnet test
        --filter '(Execution!=Manual)'
        --no-build
        --configuration ${{ matrix.configuration }}
        --logger "console;verbosity=normal"
        --logger "trx"
        --logger "GitHubActions;summary.includePassedTests=true;summary.includeSkippedTests=true"
        --blame
        --blame-hang-timeout 7m
        --blame-crash
        --results-directory testresults
        --collect "XPlat Code Coverage" -- RunConfiguration.CollectSourceInformation=true

    - name: Upload test results artifact
      if: always()
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      with:
        name: testresults-${{ matrix.os }}-${{ matrix.configuration }}
        path: testresults/**

  publish-coverage:
    if: github.actor != 'dependabot[bot]'
    needs: build
    uses: ./.github/workflows/ci-code-coverage.yml
    secrets: inherit
