name: Code Coverage

on:
  workflow_call:

permissions:
  contents: read

jobs:
  publish-coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Setup .NET
        uses: actions/setup-dotnet@67a3573c9a986a3f9c594539f4ab511d57bb3ce9 # v4.3.1
        with:
          dotnet-version: |
            9.0.x
            8.0.x

      - name: Download test results
        uses: actions/download-artifact@95815c38cf2ff2164869cbab79da8d1f422bc89e # v4.2.1
        with:
          pattern: testresults-*

      - name: Combine coverage reports
        uses: danielpalme/ReportGenerator-GitHub-Action@5.4.4
        with:
          reports: "**/*.cobertura.xml"
          targetdir: "${{ github.workspace }}/report"
          reporttypes: "HtmlSummary;Cobertura;MarkdownSummary;MarkdownSummaryGithub"
          verbosity: "Info"
          title: "Code Coverage"
          tag: "${{ github.run_number }}_${{ github.run_id }}"
          customSettings: ""
          toolpath: "reportgeneratortool"

      - name: Upload combined coverage XML
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: coverage
          path: ${{ github.workspace }}/report
          retention-days: 7

      - name: Publish code coverage report
        uses: irongut/CodeCoverageSummary@51cc3a756ddcd398d447c044c02cb6aa83fdae95 # v1.3.0
        with:
          filename: "report/Cobertura.xml"
          badge: true
          fail_below_min: true
          format: markdown
          hide_branch_rate: false
          hide_complexity: false
          indicators: true
          output: both
          thresholds: "60 80"

      - name: Upload combined coverage markdown
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: coverage-markdown
          path: ${{ github.workspace }}/code-coverage-results.md
          retention-days: 7

      - name: Coverage on step summary
        if: always()
        run: cat "${{ github.workspace }}/report/SummaryGithub.md" >> $GITHUB_STEP_SUMMARY
