<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <System9Version>9.0.5</System9Version>
    <System10Version>10.0.0-preview.4.25258.110</System10Version>
    <MicrosoftExtensionsAIVersion>9.7.1</MicrosoftExtensionsAIVersion>
  </PropertyGroup>

  <!-- Product dependencies netstandard -->
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
    <PackageVersion Include="Microsoft.Bcl.Memory" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.3" />
    <PackageVersion Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
    <PackageVersion Include="System.IO.Pipelines" Version="8.0.0" />
    <PackageVersion Include="System.Text.Json" Version="8.0.6" />
    <PackageVersion Include="System.Threading.Channels" Version="8.0.0" />
  </ItemGroup>

  <!-- Product dependencies LTS -->
  <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.3" />
    <PackageVersion Include="System.IO.Pipelines" Version="8.0.0" />
  </ItemGroup>

  <!-- Product dependencies .NET 9 -->
  <ItemGroup Condition="'$(TargetFramework)' == 'net9.0'">
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.IdentityModel.Tokens" Version="8.9.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="$(System9Version)" />
    <PackageVersion Include="System.IO.Pipelines" Version="$(System9Version)" />
  </ItemGroup>

  <!-- Product dependencies shared -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.Extensions.AI.Abstractions" Version="$(MicrosoftExtensionsAIVersion)" />
    <PackageVersion Include="Microsoft.Extensions.AI" Version="$(MicrosoftExtensionsAIVersion)" />
    <PackageVersion Include="System.Net.ServerSentEvents" Version="$(System10Version)" />
  </ItemGroup>

  <ItemGroup>

    <!-- Build Infra & Packaging -->
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />

    <!-- Testing dependencies -->
    <PackageVersion Include="Anthropic.SDK" Version="5.4.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="GitHubActionsTestLogger" Version="2.4.1" />
    <PackageVersion Include="Microsoft.Extensions.AI.OpenAI" Version="9.7.0-preview.1.25356.2" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="$(System9Version)" />
    <PackageVersion Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.5.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="OpenTelemetry" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.InMemory" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0"  />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0"  />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="9.0.1" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.Debug" Version="3.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageVersion Include="Serilog" Version="4.3.0" />
    <PackageVersion Include="System.Linq.AsyncEnumerable" Version="$(System10Version)" />
    <PackageVersion Include="xunit.v3" Version="2.0.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.1" />
    <PackageVersion Include="System.Net.Http" Version="4.3.4" />
    <PackageVersion Include="JsonSchema.Net" Version="7.3.4" />
  </ItemGroup>
</Project>