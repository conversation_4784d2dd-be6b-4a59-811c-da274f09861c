using ModelContextProtocol;
using ModelContextProtocol.Server;
using System.ComponentModel;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace QuickstartWeatherServer.Tools;

/// <summary>
/// 示例天气工具类
/// </summary>
[McpServerToolType]
public sealed class WeatherTools
{
    [McpServerTool(UseStructuredContent = true), Description("获取指定地点的当前天气")]
    public static async Task<WeatherResult> GetWeather(
        [Description("要查询天气的地点")] string location,
        WeatcherPosInput pos,
        HttpClient httpClient)
    {
        try
        {
            // 这里是简化的实现，实际应该调用真实的天气 API
            await Task.Delay(100); // 模拟 API 调用
            return new WeatherResult
            {
                Location = location,
                Temperature = 22,
                TemperatureUnit = "°C",
                Condition = "晴朗",
                Description = $"{location} 当前天气: 晴朗, 22°C"
            };
        }
        catch (Exception ex)
        {
            return new WeatherResult
            {
                Location = location,
                Error = $"获取 {location} 天气时出错: {ex.Message}",
                SubNodeResult = new SubNodeResult() { SubName = location, SubNode3 = new SubNode3() { SubName3 = "MyName3" } }
            };
        }
    }

    [McpServerTool(UseStructuredContent = true), Description("获取指定地点的天气预报")]
    public static async Task<ForecastResult> GetForecast(
        [Description("要查询预报的地点")] string location,
        [Description("预报天数")] int days = 3,
        HttpClient httpClient = null!)
    {
        try
        {
            await Task.Delay(200); // 模拟 API 调用
            return new ForecastResult
            {
                Location = location,
                Days = days,
                MinTemperature = 18,
                MaxTemperature = 25,
                TemperatureUnit = "°C",
                Condition = "大部分时间晴朗",
                Description = $"{location} {days}天预报: 大部分时间晴朗，气温 18°C 到 25°C"
            };
        }
        catch (Exception ex)
        {
            return new ForecastResult
            {
                Location = location,
                Days = days,
                Error = $"获取 {location} 预报时出错: {ex.Message}"
            };
        }
    }
}

[Description("经纬度坐标")]
public class WeatcherPosInput
{
    [Description("纬度")]
    public double Latitude { get; set; }

    [Description("经度")]
    public double Longitude { get; set; }
}
/// <summary>
/// 天气查询结果
/// </summary>
public class WeatherResult
{
    /// <summary>
    /// 查询的地点
    /// </summary>
    [JsonPropertyName("location")]
    [Description("查询的地点")]
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// 温度
    /// </summary>
    [JsonPropertyName("temperature")]
    [Description("当前温度")]
    public double? Temperature { get; set; }

    /// <summary>
    /// 温度单位
    /// </summary>
    [JsonPropertyName("temperatureUnit")]
    [Description("温度单位")]
    public string? TemperatureUnit { get; set; }

    /// <summary>
    /// 天气状况
    /// </summary>
    [JsonPropertyName("condition")]
    [Description("天气状况")]
    public string? Condition { get; set; }

    /// <summary>
    /// 描述信息
    /// </summary>
    [JsonPropertyName("description")]
    [Description("天气描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("error")]
    [Description("错误信息")]
    public string? Error { get; set; }

    /// <summary>
    /// 子节点测试
    /// </summary>
    [JsonPropertyName("subNodeResult")]
    [Description("子节点测试")]
    public SubNodeResult? SubNodeResult { get; set; }


}

/// <summary>
/// 天气预报结果
/// </summary>
public class ForecastResult
{
    /// <summary>
    /// 查询的地点
    /// </summary>
    [JsonPropertyName("location")]
    [Description("查询的地点")]
    public string Location { get; set; } = string.Empty;

    /// <summary>
    /// 预报天数
    /// </summary>
    [JsonPropertyName("days")]
    [Description("预报天数")]
    public int Days { get; set; }

    /// <summary>
    /// 最低温度
    /// </summary>
    [JsonPropertyName("minTemperature")]
    [Description("最低温度")]
    public double? MinTemperature { get; set; }

    /// <summary>
    /// 最高温度
    /// </summary>
    [JsonPropertyName("maxTemperature")]
    [Description("最高温度")]
    public double? MaxTemperature { get; set; }

    /// <summary>
    /// 温度单位
    /// </summary>
    [JsonPropertyName("temperatureUnit")]
    [Description("温度单位")]
    public string? TemperatureUnit { get; set; }

    /// <summary>
    /// 天气状况
    /// </summary>
    [JsonPropertyName("condition")]
    [Description("天气状况")]
    public string? Condition { get; set; }

    /// <summary>
    /// 描述信息
    /// </summary>
    [JsonPropertyName("description")]
    [Description("预报描述")]
    public string? Description { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("error")]
    [Description("错误信息")]
    public string? Error { get; set; }
}


/// <summary>
/// 子节点测试
/// </summary>
public class SubNodeResult
{
    /// <summary>
    /// 子节点测试
    /// </summary>
    [Description("子节点测试")]
    public string SubName { get; set; } = string.Empty;

    /// <summary>
    /// 子节点3测试
    /// </summary>
    [Description("子节点3测试")]
    public required SubNode3 SubNode3 { get; set; }


}

/// <summary>
/// 子节点测试
/// </summary>
public class SubNode3
{
    /// <summary>
    /// 子节点3测试
    /// </summary>
    [Description("子节点3测试")]
    public string SubName3 { get; set; } = "MY3";
}