using Microsoft.Extensions.Logging;
using ModelContextProtocol.Client;
using ModelContextProtocol.Protocol;
using SimpleMCPClient;
using System.Diagnostics;
using System.Net;
using System.Text;
using System.Web;

Console.WriteLine("=== Simple MCP Client Demo ===");
Console.WriteLine("Choose client mode:");
Console.WriteLine("1. Protected MCP Server (with OAuth authentication)");
Console.WriteLine("2. Simple Stdio Server (no authentication)");
Console.WriteLine("3. Simple HTTP Server (no authentication)");
Console.Write("Enter your choice (1-3): ");

var choice = Console.ReadLine();

// Create logger factory for debugging
var consoleLoggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddConsole().SetMinimumLevel(LogLevel.Information);
});

var logger = consoleLoggerFactory.CreateLogger<SimpleClientNoAuth>();
var simpleClient = new SimpleClientNoAuth(logger);

switch (choice)
{
    case "1":
        await RunProtectedClientAsync(consoleLoggerFactory);
        break;
    case "2":
        await simpleClient.RunStdioClientAsync();
        break;
    case "3":
        Console.Write("Enter server URL (default: http://localhost:8080/): ");
        var url = Console.ReadLine();
        if (string.IsNullOrWhiteSpace(url))
            url = "http://localhost:8080/";
        await simpleClient.RunHttpClientAsync(url);
        break;
    default:
        Console.WriteLine("Invalid choice. Exiting...");
        return;
}

/// <summary>
/// 运行受保护的MCP客户端（需要OAuth认证）
/// </summary>
static async Task RunProtectedClientAsync(ILoggerFactory loggerFactory)
{
    var serverUrl = "http://localhost:7071/";

    Console.WriteLine("Protected MCP Client");
    Console.WriteLine($"Connecting to weather server at {serverUrl}...");
    Console.WriteLine();

    // Create HTTP client
    var httpClient = new HttpClient();

    try
    {
        // Create transport with OAuth configuration
        var transport = new SseClientTransport(new()
        {
            Endpoint = new Uri(serverUrl),
            Name = "Simple Weather Client",
            OAuth = new()
            {
                ClientName = "SimpleMcpClient",
                RedirectUri = new Uri("http://localhost:1180/callback"),
                AuthorizationRedirectDelegate = HandleAuthorizationUrlAsync,
            }
        }, httpClient, loggerFactory);

        // Create MCP client
        var client = await McpClientFactory.CreateAsync(transport, loggerFactory: loggerFactory);

        Console.WriteLine("Connected to MCP server successfully!");
        Console.WriteLine();

        // List available tools
        var tools = await client.ListToolsAsync();
        if (tools.Count == 0)
        {
            Console.WriteLine("No tools available on the server.");
            return;
        }

        Console.WriteLine($"Found {tools.Count} tools on the server:");
        foreach (var tool in tools)
        {
            Console.WriteLine($"  - {tool.Name}: {tool.Description}");
        }
        Console.WriteLine();

        // Test get_alerts tool
        await TestGetAlertsAsync(client);

        // Test get_forecast tool
        await TestGetForecastAsync(client);

        Console.WriteLine("All tests completed successfully!");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error: {ex.Message}");
        if (ex.InnerException != null)
        {
            Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
        }
    }
    finally
    {
        httpClient.Dispose();
    }
}

/// <summary>
/// Test the get_alerts tool
/// </summary>
static async Task TestGetAlertsAsync(IMcpClient client)
{
    Console.WriteLine("=== Testing get_alerts tool ===");
    
    try
    {
        var result = await client.CallToolAsync(
            "get_alerts",
            new Dictionary<string, object?> { { "state", "CA" } }
        );

        Console.WriteLine("get_alerts result:");
        foreach (var content in result.Content)
        {
            if (content is TextContentBlock textContent)
            {
                Console.WriteLine(textContent.Text);
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error calling get_alerts: {ex.Message}");
    }
    
    Console.WriteLine();
}

/// <summary>
/// Test the get_forecast tool
/// </summary>
static async Task TestGetForecastAsync(IMcpClient client)
{
    Console.WriteLine("=== Testing get_forecast tool ===");
    
    try
    {
        // Use coordinates for San Francisco
        var result = await client.CallToolAsync(
            "get_forecast",
            new Dictionary<string, object?> 
            { 
                { "latitude", 37.7749 }, 
                { "longitude", -122.4194 } 
            }
        );

        Console.WriteLine("get_forecast result:");
        foreach (var content in result.Content)
        {
            if (content is TextContentBlock textContent)
            {
                Console.WriteLine(textContent.Text);
            }
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error calling get_forecast: {ex.Message}");
    }
    
    Console.WriteLine();
}

/// <summary>
/// Handles the OAuth authorization URL by starting a local HTTP server and opening a browser.
/// </summary>
/// <param name="authorizationUrl">The authorization URL to open in the browser.</param>
/// <param name="redirectUri">The redirect URI where the authorization code will be sent.</param>
/// <param name="cancellationToken">The cancellation token.</param>
/// <returns>The authorization code extracted from the callback, or null if the operation failed.</returns>
static async Task<string?> HandleAuthorizationUrlAsync(Uri authorizationUrl, Uri redirectUri, CancellationToken cancellationToken)
{
    Console.WriteLine("Starting OAuth authorization flow...");
    Console.WriteLine($"Opening browser to: {authorizationUrl}");

    var listenerPrefix = redirectUri.GetLeftPart(UriPartial.Authority);
    if (!listenerPrefix.EndsWith("/")) listenerPrefix += "/";

    using var listener = new HttpListener();
    listener.Prefixes.Add(listenerPrefix);

    try
    {
        listener.Start();
        Console.WriteLine($"Listening for OAuth callback on: {listenerPrefix}");

        OpenBrowser(authorizationUrl);

        var context = await listener.GetContextAsync();
        var query = HttpUtility.ParseQueryString(context.Request.Url?.Query ?? string.Empty);
        var code = query["code"];
        var error = query["error"];

        string responseHtml = "<html><body><h1>Authentication complete</h1><p>You can close this window now.</p></body></html>";
        byte[] buffer = Encoding.UTF8.GetBytes(responseHtml);
        context.Response.ContentLength64 = buffer.Length;
        context.Response.ContentType = "text/html";
        context.Response.OutputStream.Write(buffer, 0, buffer.Length);
        context.Response.Close();

        if (!string.IsNullOrEmpty(error))
        {
            Console.WriteLine($"Auth error: {error}");
            return null;
        }

        if (string.IsNullOrEmpty(code))
        {
            Console.WriteLine("No authorization code received");
            return null;
        }

        Console.WriteLine("Authorization code received successfully.");
        return code;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error getting auth code: {ex.Message}");
        return null;
    }
    finally
    {
        if (listener.IsListening) listener.Stop();
    }
}

/// <summary>
/// Opens the specified URL in the default browser.
/// </summary>
/// <param name="url">The URL to open.</param>
static void OpenBrowser(Uri url)
{
    try
    {
        var psi = new ProcessStartInfo
        {
            FileName = url.ToString(),
            UseShellExecute = true
        };
        Process.Start(psi);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error opening browser. {ex.Message}");
        Console.WriteLine($"Please manually open this URL: {url}");
    }
}
