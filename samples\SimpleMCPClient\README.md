# Simple MCP Client

这是一个简单的MCP（Model Context Protocol）客户端示例，演示如何连接和调用不同类型的MCP服务器的工具方法。

## 功能特性

- **多种传输方式支持**：
  - Stdio传输（进程间通信）
  - HTTP传输（Server-Sent Events）
  - 支持OAuth认证的HTTP传输

- **工具方法调用**：
  - `get_alerts`: 获取美国各州的天气警报
  - `get_forecast`: 获取指定经纬度的天气预报
  - 自动发现和测试服务器上的其他工具

- **完整的错误处理和日志记录**

## 前置条件

1. 确保ProtectedMCPServer正在运行（默认端口7071）
2. 确保OAuth服务器正在运行（默认端口7029）
3. .NET 9.0 SDK

## 使用方法

### 快速开始

运行演示脚本（推荐）：

```powershell
# Windows PowerShell
.\demo.ps1

# 或者直接运行
dotnet run
```

### 手动运行步骤

#### 选项1：Stdio传输（无认证）

```bash
cd samples/SimpleMCPClient
dotnet run
# 选择选项 2
```

这将自动启动QuickstartWeatherServer并通过Stdio进行通信。

#### 选项2：HTTP传输（OAuth认证）

1. 启动OAuth服务器（通常在端口7029）
2. 启动ProtectedMCPServer：

```bash
cd samples/ProtectedMCPServer
dotnet run
```

3. 运行客户端：

```bash
cd samples/SimpleMCPClient
dotnet run
# 选择选项 1
```

#### 选项3：自定义HTTP服务器

```bash
cd samples/SimpleMCPClient
dotnet run
# 选择选项 3，然后输入服务器URL
```

## 工具方法测试

### get_alerts 工具
- 测试获取加利福尼亚州(CA)的天气警报
- 如果没有活跃警报，会显示"No active alerts for this state."

### get_forecast 工具
- 测试获取旧金山地区的天气预报
- 使用坐标：纬度 37.7749，经度 -122.4194

## 代码结构

- `Program.cs`: 主程序文件，包含客户端逻辑和OAuth处理
- `SimpleMCPClient.csproj`: 项目文件，定义依赖关系

## 主要组件

1. **SseClientTransport**: 使用Server-Sent Events传输协议
2. **OAuth认证**: 处理受保护资源的访问
3. **工具调用**: 演示如何调用MCP服务器的工具方法

## 错误处理

客户端包含完整的错误处理机制：
- 连接错误
- 认证失败
- 工具调用异常
- 网络超时

## 自定义配置

可以修改以下配置：
- 服务器URL（默认：http://localhost:7071/）
- 回调端口（默认：1180）
- 客户端名称
- 测试参数（州代码、坐标等）
