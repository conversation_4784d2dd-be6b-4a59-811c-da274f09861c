using Microsoft.Extensions.Logging;
using ModelContextProtocol.Client;
using ModelContextProtocol.Protocol;

namespace SimpleMCPClient;

/// <summary>
/// 简单的MCP客户端，不需要认证，用于连接到标准的MCP服务器
/// </summary>
public class SimpleClientNoAuth
{
    private readonly ILogger<SimpleClientNoAuth> _logger;

    public SimpleClientNoAuth(ILogger<SimpleClientNoAuth> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 连接到Stdio传输的MCP服务器并测试工具调用
    /// </summary>
    public async Task RunStdioClientAsync()
    {
        Console.WriteLine("=== Simple MCP Client (Stdio Transport) ===");
        Console.WriteLine("Connecting to QuickstartWeatherServer...");
        Console.WriteLine();

        try
        {
            // 创建Stdio传输客户端
            var clientTransport = new StdioClientTransport(new StdioClientTransportOptions
            {
                Name = "SimpleWeatherClient",
                Command = "dotnet",
                Arguments = ["run", "--project", "../QuickstartWeatherServer"],
                WorkingDirectory = Path.GetFullPath("../QuickstartWeatherServer")
            });

            // 创建MCP客户端
            await using var client = await McpClientFactory.CreateAsync(clientTransport);

            Console.WriteLine("Connected to MCP server successfully!");
            Console.WriteLine();

            // 列出可用工具
            await ListAvailableToolsAsync(client);

            // 测试工具调用
            await TestToolsAsync(client);

            Console.WriteLine("All tests completed successfully!");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running stdio client");
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// 连接到HTTP传输的MCP服务器并测试工具调用
    /// </summary>
    public async Task RunHttpClientAsync(string serverUrl = "http://localhost:8080/")
    {
        Console.WriteLine("=== Simple MCP Client (HTTP Transport) ===");
        Console.WriteLine($"Connecting to server at {serverUrl}...");
        Console.WriteLine();

        try
        {
            // 创建HTTP客户端
            using var httpClient = new HttpClient();

            // 创建SSE传输客户端（不需要OAuth）
            var transport = new SseClientTransport(new()
            {
                Endpoint = new Uri(serverUrl),
                Name = "Simple HTTP Client"
            }, httpClient);

            // 创建MCP客户端
            await using var client = await McpClientFactory.CreateAsync(transport);

            Console.WriteLine("Connected to MCP server successfully!");
            Console.WriteLine();

            // 列出可用工具
            await ListAvailableToolsAsync(client);

            // 测试工具调用
            await TestToolsAsync(client);

            Console.WriteLine("All tests completed successfully!");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running HTTP client");
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    /// <summary>
    /// 列出服务器上可用的工具
    /// </summary>
    private async Task ListAvailableToolsAsync(IMcpClient client)
    {
        var tools = await client.ListToolsAsync();
        
        if (tools.Count == 0)
        {
            Console.WriteLine("No tools available on the server.");
            return;
        }

        Console.WriteLine($"Found {tools.Count} tools on the server:");
        foreach (var tool in tools)
        {
            Console.WriteLine($"  - {tool.Name}: {tool.Description}");
            
            // 显示工具参数
            if (tool.JsonSchema.TryGetProperty("properties", out var properties))
            {
                Console.WriteLine("    Parameters:");
                foreach (var param in properties.EnumerateObject())
                {
                    var description = param.Value.TryGetProperty("description", out var desc)
                        ? desc.GetString()
                        : "No description";
                    var type = param.Value.TryGetProperty("type", out var typeNode)
                        ? typeNode.GetString()
                        : "unknown";

                    Console.WriteLine($"      - {param.Name} ({type}): {description}");
                }
            }
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 测试各种工具调用
    /// </summary>
    private async Task TestToolsAsync(IMcpClient client)
    {
        var tools = await client.ListToolsAsync();

        // 测试GetWeather工具（优先测试）
        var getWeatherTool = tools.FirstOrDefault(t => t.Name == "GetWeather");
        if (getWeatherTool != null)
        {
            await TestGetWeatherAsync(client);
        }

        // 测试get_alerts工具
        var alertsTool = tools.FirstOrDefault(t => t.Name == "get_alerts");
        if (alertsTool != null)
        {
            await TestGetAlertsAsync(client);
        }

        // 测试get_forecast工具
        var forecastTool = tools.FirstOrDefault(t => t.Name == "get_forecast");
        if (forecastTool != null)
        {
            await TestGetForecastAsync(client);
        }

        // 测试GetForecast工具
        var getForecastTool = tools.FirstOrDefault(t => t.Name == "GetForecast");
        if (getForecastTool != null)
        {
            await TestGetForecastToolAsync(client);
        }

        // 如果有其他工具，也可以测试
        foreach (var tool in tools.Where(t => t.Name != "get_alerts" && t.Name != "get_forecast"))
        {
            await TestGenericToolAsync(client, tool);
        }
    }

    /// <summary>
    /// 测试GetWeather工具
    /// </summary>
    private async Task TestGetWeatherAsync(IMcpClient client)
    {
        Console.WriteLine("=== Testing GetWeather tool ===");

        try
        {
            var result = await client.CallToolAsync(
                "GetWeather",
                new Dictionary<string, object?>
                {
                    { "location", "北京" },
                    { "pos", new { Latitude = 39.9042, Longitude = 116.4074 } }
                }
            );

            Console.WriteLine("GetWeather result:");
            DisplayToolResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling GetWeather");
            Console.WriteLine($"Error calling GetWeather: {ex.Message}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 测试GetForecast工具
    /// </summary>
    private async Task TestGetForecastToolAsync(IMcpClient client)
    {
        Console.WriteLine("=== Testing GetForecast tool ===");

        try
        {
            var result = await client.CallToolAsync(
                "GetForecast",
                new Dictionary<string, object?>
                {
                    { "location", "上海" },
                    { "days", 5 }
                }
            );

            Console.WriteLine("GetForecast result:");
            DisplayToolResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling GetForecast");
            Console.WriteLine($"Error calling GetForecast: {ex.Message}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 测试get_alerts工具
    /// </summary>
    private async Task TestGetAlertsAsync(IMcpClient client)
    {
        Console.WriteLine("=== Testing get_alerts tool ===");
        
        try
        {
            var result = await client.CallToolAsync(
                "get_alerts",
                new Dictionary<string, object?> { { "state", "CA" } }
            );

            Console.WriteLine("get_alerts result:");
            DisplayToolResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling get_alerts");
            Console.WriteLine($"Error calling get_alerts: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 测试get_forecast工具
    /// </summary>
    private async Task TestGetForecastAsync(IMcpClient client)
    {
        Console.WriteLine("=== Testing get_forecast tool ===");
        
        try
        {
            // 使用旧金山的坐标
            var result = await client.CallToolAsync(
                "get_forecast",
                new Dictionary<string, object?> 
                { 
                    { "latitude", 37.7749 }, 
                    { "longitude", -122.4194 } 
                }
            );

            Console.WriteLine("get_forecast result:");
            DisplayToolResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling get_forecast");
            Console.WriteLine($"Error calling get_forecast: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 测试通用工具（不带参数）
    /// </summary>
    private async Task TestGenericToolAsync(IMcpClient client, McpClientTool tool)
    {
        Console.WriteLine($"=== Testing {tool.Name} tool ===");
        
        try
        {
            var result = await client.CallToolAsync(tool.Name, new Dictionary<string, object?>());

            Console.WriteLine($"{tool.Name} result:");
            DisplayToolResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling {ToolName}", tool.Name);
            Console.WriteLine($"Error calling {tool.Name}: {ex.Message}");
        }
        
        Console.WriteLine();
    }

    /// <summary>
    /// 显示工具调用结果
    /// </summary>
    private void DisplayToolResult(CallToolResult result)
    {
        if (result.Content?.Count > 0)
        {
            foreach (var content in result.Content)
            {
                switch (content)
                {
                    case TextContentBlock textContent:
                        Console.WriteLine(textContent.Text);
                        break;
                    case ImageContentBlock imageContent:
                        Console.WriteLine($"[Image: {imageContent.Data?.Length ?? 0} bytes]");
                        break;
                    default:
                        Console.WriteLine($"[Unknown content type: {content.GetType().Name}]");
                        break;
                }
            }
        }
        else
        {
            Console.WriteLine("No content returned.");
        }
    }
}
