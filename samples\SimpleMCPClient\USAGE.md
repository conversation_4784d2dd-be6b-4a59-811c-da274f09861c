# SimpleMCPClient 使用指南

## 概述

SimpleMCPClient 是一个演示如何创建和使用MCP客户端的完整示例。它支持多种传输方式和认证模式，可以连接到不同类型的MCP服务器。

## 快速开始

### 1. 编译项目

```bash
cd samples/SimpleMCPClient
dotnet build
```

### 2. 运行客户端

```bash
dotnet run
```

运行后会看到选择菜单：

```
=== Simple MCP Client Demo ===
Choose client mode:
1. Protected MCP Server (with OAuth authentication)
2. Simple Stdio Server (no authentication)
3. Simple HTTP Server (no authentication)
Enter your choice (1-3):
```

## 运行模式详解

### 模式1：受保护的MCP服务器（OAuth认证）

**适用场景**：连接到需要OAuth认证的MCP服务器

**前置条件**：
- OAuth服务器运行在端口7029
- ProtectedMCPServer运行在端口7071

**启动步骤**：
1. 启动OAuth服务器
2. 启动ProtectedMCPServer：
   ```bash
   cd samples/ProtectedMCPServer
   dotnet run
   ```
3. 运行客户端并选择选项1

**认证流程**：
- 客户端会自动打开浏览器进行OAuth认证
- 完成认证后返回客户端继续执行

### 模式2：Stdio传输（无认证）

**适用场景**：连接到使用标准输入输出的MCP服务器

**特点**：
- 客户端自动启动QuickstartWeatherServer
- 通过进程间通信进行数据交换
- 无需手动启动服务器

**使用方法**：
- 运行客户端并选择选项2
- 客户端会自动处理服务器启动和通信

### 模式3：HTTP传输（无认证）

**适用场景**：连接到HTTP端点的MCP服务器（不需要认证）

**使用方法**：
- 运行客户端并选择选项3
- 输入服务器URL（默认：http://localhost:8080/）

## 工具方法测试

客户端会自动测试以下工具方法：

### get_alerts
- **功能**：获取美国各州的天气警报
- **参数**：state (string) - 美国州的2字母缩写
- **测试用例**：获取加利福尼亚州(CA)的警报

### get_forecast
- **功能**：获取指定位置的天气预报
- **参数**：
  - latitude (number) - 纬度
  - longitude (number) - 经度
- **测试用例**：获取旧金山地区的天气预报（37.7749, -122.4194）

## 输出示例

### 工具列表
```
Found 2 tools on the server:
  - get_forecast: Get weather forecast for a location.
    Parameters:
      - latitude (number): Latitude of the location.
      - longitude (number): Longitude of the location.
  - get_alerts: Get weather alerts for a US state.
    Parameters:
      - state (string): The US state to get alerts for. Use the 2 letter abbreviation for the state (e.g. NY).
```

### 天气警报结果
```
=== Testing get_alerts tool ===
get_alerts result:
Event: Severe Thunderstorm Warning
Area: Shasta, CA
Severity: Severe
Description: [详细的天气警报描述]
Instruction: [安全指导]
```

### 天气预报结果
```
=== Testing get_forecast tool ===
get_forecast result:
Today
Temperature: 68°F
Wind: 6 to 14 mph WSW
Forecast: Mostly sunny. High near 68...
---
Tonight
Temperature: 57°F
Wind: 6 to 14 mph WSW
Forecast: Partly cloudy, with a low around 57...
```

## 代码结构

### 主要文件

- **Program.cs**: 主程序入口，处理用户选择和OAuth认证
- **SimpleClientNoAuth.cs**: 简单客户端实现，支持Stdio和HTTP传输
- **SimpleMCPClient.csproj**: 项目配置文件

### 关键组件

1. **传输层**：
   - `StdioClientTransport`: 标准输入输出传输
   - `SseClientTransport`: Server-Sent Events HTTP传输

2. **认证**：
   - OAuth 2.0流程处理
   - 本地HTTP服务器接收回调

3. **工具调用**：
   - 自动发现服务器工具
   - 参数化工具调用
   - 结果解析和显示

## 错误处理

客户端包含完整的错误处理：

- 连接失败
- 认证错误
- 工具调用异常
- 网络超时
- 服务器不可用

## 扩展和自定义

### 添加新的工具测试

在`SimpleClientNoAuth.cs`中的`TestToolsAsync`方法中添加新的测试逻辑：

```csharp
private async Task TestCustomToolAsync(IMcpClient client)
{
    var result = await client.CallToolAsync(
        "custom_tool",
        new Dictionary<string, object?> { { "param", "value" } }
    );
    
    DisplayToolResult(result);
}
```

### 修改测试参数

可以在相应的测试方法中修改参数：

```csharp
// 修改测试的州代码
{ "state", "NY" }  // 改为纽约州

// 修改测试的坐标
{ "latitude", 40.7128 }, { "longitude", -74.0060 }  // 改为纽约市
```

## 故障排除

### 常见问题

1. **编译错误**：确保安装了.NET 9.0 SDK
2. **连接失败**：检查服务器是否正在运行
3. **认证失败**：确保OAuth服务器配置正确
4. **工具调用失败**：检查参数格式和服务器日志

### 调试技巧

- 启用详细日志记录
- 检查服务器控制台输出
- 使用网络抓包工具分析HTTP流量
- 查看OAuth认证流程的浏览器网络面板
