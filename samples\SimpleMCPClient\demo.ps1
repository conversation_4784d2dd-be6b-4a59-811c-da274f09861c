# Simple MCP Client Demo Script
# 这个脚本演示如何运行不同类型的MCP客户端

Write-Host "=== Simple MCP Client Demo ===" -ForegroundColor Green
Write-Host ""

# 检查.NET是否安装
try {
    $dotnetVersion = dotnet --version
    Write-Host "Found .NET version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: .NET is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Available demo options:" -ForegroundColor Yellow
Write-Host "1. Test with QuickstartWeatherServer (Stdio transport, no auth)"
Write-Host "2. Test with ProtectedMCPServer (HTTP transport, OAuth auth)"
Write-Host "3. Build and run client interactively"
Write-Host ""

$choice = Read-Host "Enter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host "=== Demo 1: Stdio Transport (No Authentication) ===" -ForegroundColor Cyan
        Write-Host ""
        
        # 检查QuickstartWeatherServer是否存在
        if (!(Test-Path "../QuickstartWeatherServer/QuickstartWeatherServer.csproj")) {
            Write-Host "Error: QuickstartWeatherServer not found" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Building SimpleMCPClient..." -ForegroundColor Yellow
        dotnet build
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build failed" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Running client with Stdio transport..." -ForegroundColor Yellow
        Write-Host "The client will automatically start QuickstartWeatherServer" -ForegroundColor Gray
        Write-Host ""
        
        # 运行客户端，选择选项2（Stdio）
        echo "2" | dotnet run
    }
    
    "2" {
        Write-Host "=== Demo 2: HTTP Transport (OAuth Authentication) ===" -ForegroundColor Cyan
        Write-Host ""
        
        # 检查ProtectedMCPServer是否存在
        if (!(Test-Path "../ProtectedMCPServer/ProtectedMCPServer.csproj")) {
            Write-Host "Error: ProtectedMCPServer not found" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "This demo requires manual setup:" -ForegroundColor Yellow
        Write-Host "1. Start the OAuth server (usually on port 7029)" -ForegroundColor Gray
        Write-Host "2. Start ProtectedMCPServer in another terminal:" -ForegroundColor Gray
        Write-Host "   cd ../ProtectedMCPServer && dotnet run" -ForegroundColor Gray
        Write-Host ""
        
        $continue = Read-Host "Have you started both servers? (y/n)"
        if ($continue -ne "y") {
            Write-Host "Please start the servers first" -ForegroundColor Yellow
            exit 0
        }
        
        Write-Host "Building SimpleMCPClient..." -ForegroundColor Yellow
        dotnet build
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build failed" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Running client with HTTP transport and OAuth..." -ForegroundColor Yellow
        Write-Host "A browser window will open for authentication" -ForegroundColor Gray
        Write-Host ""
        
        # 运行客户端，选择选项1（Protected）
        echo "1" | dotnet run
    }
    
    "3" {
        Write-Host "=== Demo 3: Interactive Mode ===" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "Building SimpleMCPClient..." -ForegroundColor Yellow
        dotnet build
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build failed" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Running client in interactive mode..." -ForegroundColor Yellow
        Write-Host "You can choose the transport type when prompted" -ForegroundColor Gray
        Write-Host ""
        
        dotnet run
    }
    
    default {
        Write-Host "Invalid choice" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "Demo completed!" -ForegroundColor Green
