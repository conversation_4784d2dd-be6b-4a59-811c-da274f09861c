<Project>
  <Import Project="..\Directory.Build.props" />

  <PropertyGroup>
    <PackageProjectUrl>https://github.com/modelcontextprotocol/csharp-sdk</PackageProjectUrl>
    <RepositoryUrl>https://github.com/modelcontextprotocol/csharp-sdk</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <VersionPrefix>0.3.0</VersionPrefix>
    <VersionSuffix>preview.4</VersionSuffix>
    <Authors>ModelContextProtocolOfficial</Authors>
    <Copyright>© Anthropic and Contributors.</Copyright>
    <PackageTags>ModelContextProtocol;mcp;ai;llm</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageIcon>logo.png</PackageIcon>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <AssemblyOriginatorKeyFile>$(RepoRoot)\Open.snk</AssemblyOriginatorKeyFile>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\Common\Polyfills\**\*.cs" />
  </ItemGroup>

  <ItemGroup>
    <None Include="$(RepoRoot)\logo.png" Pack="true" PackagePath="\" />
  </ItemGroup>
</Project>
