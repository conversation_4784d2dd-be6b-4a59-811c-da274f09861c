﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net8.0;netstandard2.0</TargetFrameworks>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IsPackable>true</IsPackable>
    <PackageId>ModelContextProtocol.Core</PackageId>
    <Description>Core .NET SDK for the Model Context Protocol (MCP)</Description>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' != 'netstandard2.0'">
    <IsAotCompatible>true</IsAotCompatible>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\Common\Throw.cs" Link="Throw.cs" />
  </ItemGroup>

  <!-- Dependencies only needed by netstandard2.0 -->
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
    <Compile Include="..\Common\CancellableStreamReader\**\*.cs" />
    <PackageReference Include="Microsoft.Bcl.Memory" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" />
    <PackageReference Include="System.Text.Json" />
    <PackageReference Include="System.Threading.Channels" />
  </ItemGroup>

  <!-- Dependencies only needed by netstandard2.0 or net8.0 -->
  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0' or '$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="System.IO.Pipelines" />
  </ItemGroup>

  <!-- Dependencies needed by all -->
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.AI.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="System.Net.ServerSentEvents" />
  </ItemGroup>

  <ItemGroup>
    <None Include="README.md" pack="true" PackagePath="\" />
  </ItemGroup>

</Project>
