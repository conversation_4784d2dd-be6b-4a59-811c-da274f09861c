﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net9.0;net8.0;netstandard2.0</TargetFrameworks>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IsPackable>true</IsPackable>
    <PackageId>ModelContextProtocol</PackageId>
    <Description>.NET SDK for the Model Context Protocol (MCP) with hosting and dependency injection extensions.</Description>
    <PackageReadmeFile>README.md</PackageReadmeFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' != 'netstandard2.0'">
    <IsAotCompatible>true</IsAotCompatible>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\Common\Throw.cs" Link="Throw.cs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ModelContextProtocol.Core\ModelContextProtocol.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\..\README.md" pack="true" PackagePath="\" />
  </ItemGroup>

</Project>