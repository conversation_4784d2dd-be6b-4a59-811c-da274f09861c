﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFrameworks>net9.0;net8.0;net472</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <RootNamespace>ModelContextProtocol.Tests</RootNamespace>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' == 'net9.0'">
    <!-- For better test coverage, only disable reflection in one of the targets -->
    <JsonSerializerIsReflectionEnabledByDefault>false</JsonSerializerIsReflectionEnabledByDefault>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Without this, tests are currently not showing results until all tests complete
         https://xunit.net/docs/getting-started/v3/microsoft-testing-platform
    -->
    <DisableTestingPlatformServerCapability>true</DisableTestingPlatformServerCapability>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\Common\**\*.cs" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net472'">
    <Compile Include="..\..\src\Common\Throw.cs" Link="Throw.cs" />
    <Compile Include="..\..\src\Common\Polyfills\**\*.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="GitHubActionsTestLogger">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.AI" />
    <PackageReference Include="Microsoft.Extensions.AI.OpenAI" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="OpenTelemetry" />
    <PackageReference Include="OpenTelemetry.Exporter.InMemory" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="System.Linq.AsyncEnumerable" />
    <PackageReference Include="System.Net.Http" />
    <PackageReference Include="JsonSchema.Net" />
    <PackageReference Include="xunit.v3" />
    <PackageReference Include="xunit.runner.visualstudio">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\ModelContextProtocol.Core\ModelContextProtocol.Core.csproj" />
    <ProjectReference Include="..\ModelContextProtocol.TestServer\ModelContextProtocol.TestServer.csproj" />
    <ProjectReference Include="..\..\samples\TestServerWithHosting\TestServerWithHosting.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="$([System.IO.Path]::GetFullPath('$(ArtifactsBinDir)'))ModelContextProtocol.TestServer\$(Configuration)\$(TargetFramework)\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="$([System.IO.Path]::GetFullPath('$(ArtifactsBinDir)'))TestServerWithHosting\$(Configuration)\$(TargetFramework)\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
